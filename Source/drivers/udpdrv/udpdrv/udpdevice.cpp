#include "udpdevice.h"

#include "Buffer.h"

CBuffer::CBuffer(size_t _size)
{
	// һ���Է����㹻�ռ�
	m_vBuffer.reserve (std::vector<unsigned char>::size_type(_size+1));
	m_totalSize = _size;
}

CBuffer::~CBuffer(void)
{
}
bool CBuffer::isFull(void)
{
	return m_totalSize <= m_vBuffer.size ();
}
/**********************************************************************
*  ����:  ������д�뻺��
*  ����ֵ: int [����] д������ݳ���
*  �����б�:
*    _buffer: [IN]   [����] ��Ҫд�������
*    _len   : [IN]   [����] ��Ҫд������ݳ���
*
*  �汾��ʷ		
*       1.0   2010:6:1 17:15   Shenhuaibin    ʵ��
*  
**********************************************************************/	
int CBuffer::write(const void* _buffer,int _len)
{
	int index = m_vBuffer.size ();
	// �����ڴ�
	// resize ռ��cpuʱ����Խ϶�.
	m_vBuffer.resize (index + _len);
	if(!memcpy_s(&m_vBuffer[index],_len,_buffer,_len))
	{
		return _len;
	}
	return 0;
}
/**********************************************************************
*  ����:  ��ȡ����
*  ����ֵ: std::vector<unsigned char>& [����] �ö����е�buffer��
*  �����б�:
*    ��
*
*  �汾��ʷ		
*       1.0   2010:6:1 17:39   Shenhuaibin    ʵ��
*  
**********************************************************************/	
std::vector<unsigned char>& CBuffer::read(void)
{
	return m_vBuffer;
}
/**********************************************************************
*  ����:  �������
*  ����ֵ:  [����]	��
*  �����б�:
*    ��
*
*  �汾��ʷ		
*       1.0   2010:6:12 9:45   Shenhuaibin    ʵ��
*  
**********************************************************************/	
void CBuffer::clear()
{
	m_vBuffer.clear ();
}
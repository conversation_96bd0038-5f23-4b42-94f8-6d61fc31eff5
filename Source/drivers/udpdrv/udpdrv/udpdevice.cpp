#include "udpdevice.h"
#include <cstring>
#include <algorithm>

CBuffer::CBuffer(size_t size)
{
	m_vBuffer.reserve(std::vector<unsigned char>::size_type(size + 1));
	m_totalSize = size;
}

CBuffer::~<PERSON>uffer(void)
{
}

bool CBuffer::isFull(void)
{
	return m_totalSize <= m_vBuffer.size();
}

int CBuffer::write(const void* buffer, int len)
{
	if (!buffer || len <= 0)
		return 0;

	// 检查缓冲区是否有足够空间
	if (m_vBuffer.size() + len > m_totalSize)
		return 0;

	int index = m_vBuffer.size();
	m_vBuffer.resize(index + len);

	memcpy(&m_vBuffer[index], buffer, len);

	return len;
}

std::vector<unsigned char>& CBuffer::read(void)
{
	return m_vBuffer;
}

void CBuffer::clear()
{
	m_vBuffer.clear();
}


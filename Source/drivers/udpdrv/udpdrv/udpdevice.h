#ifndef UDPDEVICE_H
#define UDPDEVICE_H

#include "driversdk/cvdrivercommon.h"
#include <vector>
#include <map>
#include <mutex>
#include <string>
#include "ace/INET_Addr.h"

class CBuffer
{
public:
    CBuffer(size_t size);
	virtual ~CBuffer(void);

	// buffer区是否已满
	bool isFull(void);
	// 写入数据
	int write(const void* buffer,int len);
	// 读取数据
	std::vector<unsigned char>& read(void);
	// 清空数据
	void clear();
	
private:
	// 该buffer一次性分配的最大内存尺寸
	size_t m_totalSize;
	// buffer区
	std::vector<unsigned char> m_vBuffer;
};

class UDPDevice
{
public:
    UDPDevice(DRVHANDLE hDevice);
    virtual ~UDPDevice(void);
};

#endif
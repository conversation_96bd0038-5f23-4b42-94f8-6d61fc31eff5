#include <stdlib.h>
#include <memory.h>
#include <map>
#include <vector>
#include <algorithm>
#include <string>
#include "ace/OS_NS_sys_time.h"
#include "ace/INET_Addr.h"
#include "udpdrv.h"

CCVLog g_CVLogUDPDrv;
const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "udpdrv";

std::map<DRVHANDLE, UDPDevice*> g_mapDevices;
std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> > g_mapDataBlocks;

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogUDPDrv.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long UnInitialize()
{
    g_CVLogUDPDrv.StopLogThread();
    return DRV_SUCCESS;
}

/* 不实现 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	return DRV_SUCCESS;
}

//获取版本信息  
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
    return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
	return DRV_SUCCESS;
}

#include <stdlib.h>
#include <memory.h>
#include "ace/OS_NS_sys_time.h"
#include "udpdrv.h"

CCVLog g_CVLogUDPDrv;
const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "udpdrv";

std::map<DRVHANDLE, UDPDevice*> g_mapDevices;
std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> > g_mapDataBlocks;
CVDRIVER_EXPORTS long Begin()
{
	g_CVLogUDPDrv.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
{
	return 0;
}

CVDRIVER_EXPORTS long UnInitialize()
{
    g_CVLogUDPDrv.StopLogThread();
    return DRV_SUCCESS;
}

void CheckBlockStatus(DRVHANDLE hDevice, DRVHANDLE hDatablock, long lSuccess)
{
	if(lSuccess == DRV_SUCCESS)
		Drv_SetUserData(hDatablock, 0, 0);
	else
	{
		long lFailCountRecent = Drv_GetUserData(hDatablock, 0);
		if(lFailCountRecent > 3)	// 最近失败次数
		{
			Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);
			Drv_SetUserData(hDatablock, 0, 0); // 避免计数太大导致循环
		}
		else
			Drv_SetUserData(hDatablock, 0, lFailCountRecent + 1);
	}
}


/* 不实现 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
{
	return DRV_SUCCESS;
}

 /*  初始化数据块请求计数为0 .*/
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDatablock);

	//初始化设置包请求失败计数为0
	CheckBlockStatus(hDevice, hDatablock, DRV_SUCCESS);

	//存储设备以及数据块配置信息
	std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> >::iterator it = g_mapDataBlocks.find(hDevice);
	if (it != g_mapDataBlocks.end())
	{
		it->second.insert(make_pair(hDatablock, pDataBlock));
	}
	else
	{
		std::map<DRVHANDLE, CVDATABLOCK*> mapDBs;
		mapDBs.insert(make_pair(hDatablock, pDataBlock));
		g_mapDataBlocks.insert(make_pair(hDevice, mapDBs));
	}
	
	return DRV_SUCCESS;
}

//获取版本信息  
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
    
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
	return DRV_SUCCESS;
}

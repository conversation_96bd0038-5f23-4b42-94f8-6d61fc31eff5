/**
 * Filename        icedrv.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description
 *
 * Author          wuz<PERSON><PERSON><PERSON>
 * Version         06/18/2025    wuzheqiang    Initial Version
 **************************************************************/

#include "icedrv.h"
#include "TypeCast.h"
#include "common/CVLog.h"
#include <thread>
#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif // _WIN32

#include "common/CommHelper.h"
#include <ace/Guard_T.h>
#include <ace/Thread_Mutex.h>
#include <algorithm>
#include <boost/timer.hpp> // boost��ʱ��ͷ�ļ�
#include <chrono>
#include <map>
#include <set>
#include <sstream>
#include <unordered_map>

CCVLog g_CVLogICEDrv;
#define DRIVE_NAME "icedrv"
#define BITS_PER_BYTE 8       // һ���ֽ�ռ�õ�λ��
#define STRING_BLOCK_SIZE 256 // string���͵����ݿ�Ĭ�ϳ���

std::vector<DRVHANDLE> g_deviceHandle; // �������е�device��handle��������״̬�Ļص����ҵ���Ҫ��������״̬���豸
std::unordered_map<std::string, ICEDataBlock> g_DataBloackMap; // ���е����ݿ�
std::unordered_map<std::string, int> g_tagScanIntv;

// �ṩ���Զ� send����.
void DataReceiverI::sendData(const DSF::DataUnitSeq &dataSeq, const Ice::Current &current)
{
    static std::mutex localMtx;
    std::lock_guard<std::mutex> lock(localMtx);

    CV_INFO(g_CVLogICEDrv, "received data from server,cnt =%d", dataSeq.size());

    auto endpoint = current.con->getInfo();
    auto tcpInfo = Ice::TCPConnectionInfoPtr::dynamicCast(endpoint);

    if (tcpInfo)
    {
        std::string clientIP = tcpInfo->remoteAddress;
        int clientPort = tcpInfo->remotePort;
        CV_INFO(g_CVLogICEDrv, "Client IP: %s, Port: %d", clientIP.c_str(), clientPort);
    }

    for (const auto &data : dataSeq)
    {
        auto iter = g_DataBloackMap.find(data.strName);
        if (iter == g_DataBloackMap.end())
        {
            CV_INFO(g_CVLogICEDrv, "Tagname %s not found data block ", data.strName.c_str());
            continue;
        }
        auto &Datablock = iter->second;
        CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(Datablock.hDatablock);
        CVDEVICE *pDevice = Drv_GetDeviceInfo(Datablock.hDevice);

        char szValue[STRING_BLOCK_SIZE] = {0};

        switch (data.eType)
        {
        case DSF::ValueType::Decimal:
            memcpy(szValue, &data.dValue, sizeof(data.dValue));
            break;
        case DSF::ValueType::Integer:
            memcpy(szValue, &data.lValue, sizeof(data.lValue));
            break;
        case DSF::ValueType::Boolean:
            memcpy(szValue, &data.bValue, sizeof(data.bValue));
            break;
        case DSF::ValueType::Text:
            memcpy(szValue, data.strValue.c_str(), data.strValue.size());
            break;
        default:
            CV_INFO(g_CVLogICEDrv, "Tagname %s : eType %d not found supported type   ", data.eType,
                    data.strName.c_str());

            break;
        }

        Drv_UpdateBlockData(Datablock.hDevice, Datablock.hDatablock, szValue, 0, pDataBlock->nBlockDataSize,
                            DATA_STATUS_OK, NULL);
        Drv_UpdateDevStatus(Datablock.hDevice, DEV_STATUS_GOOD);
        CV_INFO(g_CVLogICEDrv, "Device[%s] BlockAddr[%s] size[%d] updateblockdata success. ", pDevice->pszName,
                pDataBlock->pszAddress, pDataBlock->nBlockDataSize);
    }

    CV_INFO(g_CVLogICEDrv, "Received %d entries", dataSeq.size());
}

void GetNameFromAddress(string &strAddr, const char *pszAddress)
{
    string strInAddress = pszAddress;
    // ȫ��תΪ��д
    // transform(strInAddress.begin(), strInAddress.end(), strInAddress.begin(),
    //           [](unsigned char c) { return std::toupper(c); });
    string strTXaddr = strInAddress;
    size_t pos = strTXaddr.find_first_of(':');
    if (pos != string::npos && pos != strTXaddr.size() - 1)
    {
        strAddr = strTXaddr.substr(pos + 1);
    }
    else
    {
        strAddr = strInAddress;
    }

    pos = strAddr.find_first_of('#');
    if (pos != string::npos)
        strAddr = strAddr.substr(0, pos);
}

/**
 *  ��ʼ������������EXE����ʱ�ú��������ã����ڸú�����ʵ���Զ����ʼ������.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long Begin()
{
    g_CVLogICEDrv.SetLogFileNameThread(DRIVE_NAME);
    return DRV_SUCCESS;
}

/**
 *  ��ʼ������������EXE����ʱ�ú��������ã����ڸú�����ʵ���Զ����ʼ������.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long Initialize()
{
    CV_INFO(g_CVLogICEDrv, "icedrv driver Initialize...");

    std::thread ioThread([]() {
        static ICEServer ice_server;
        ice_server.run(); // This will run in a separate thread
    });
    ioThread.detach();
    return DRV_SUCCESS;
}

/**
 *  ����EXE�˳�ʱ�ú���������.
 *  �ڸú����п����ͷ��Զ�����Դ���Ͽ��豸���ӵȲ���.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long UnInitialize()
{
    CV_INFO(g_CVLogICEDrv, "icedrv driver UnInitialize...");
    g_CVLogICEDrv.StopLogThread();
    return DRV_SUCCESS;
}

/**
 *  �����豸ʱ�ú���������.
 *  �ú�����Ҫ��Է�tcp�����豸���û�����ͨ���豸�����ȡ�豸���Ӳ�������ʼ�������豸
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
    if (NULL == hDevice)
        return EC_ICV_DRIVER_INVALID_PARAMETER;

    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    ICEDevice *pICEDevice = new ICEDevice(hDevice);
    Drv_SetUserDataPtr(hDevice, 0, static_cast<void *>(pICEDevice));

    g_deviceHandle.push_back(hDevice);
    long activeStatus = Drv_IsActiveHost();
    // �����ӣ���Ϊ���������豸������
    if (pICEDevice->m_bMultiLink == false && activeStatus == RM_STATUS_INACTIVE)
    {
        CV_INFO(g_CVLogICEDrv, "m_bMultiLink[%d] and INACTIVE", pICEDevice->m_bMultiLink);
        pICEDevice->ICEDisconnect();
        Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
        return DRV_SUCCESS;
    }
    // ������ �� �������Ҳ��Ǳ���
    CV_INFO(g_CVLogICEDrv, "m_bMultiLink[%d] and ActiveStatus[%d]", pICEDevice->m_bMultiLink, activeStatus);
    long lRet = pICEDevice->ICEConnect();
    if (lRet != DRV_SUCCESS)
    {
        pICEDevice->ICEDisconnect();
        Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
    }
    else
    {
        Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
    }
    return DRV_SUCCESS;
}

/**
 *  ����¹��ܣ�����һ���������������£���������
 *  ֻ��Ҫ������Ӧ�Ĳ�������ܻ��Լ�����
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnBatchUpdateData(int &nMsTimeOut, int &DataSize)
{
    int timeout = 100;
    int datasize = 30000;

    nMsTimeOut = timeout;
    DataSize = datasize;
    return 0;
}
/**
 *  ɾ���豸ʱ�ú���������.
 *
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    ICEDevice *pICEDevice = static_cast<ICEDevice *>(Drv_GetUserDataPtr(hDevice, 0));
    if (pICEDevice != NULL)
    {
        // �Ͽ�����
        CV_DEBUG(g_CVLogICEDrv, "OnDeviceDelete device name[%s]", pDevice->pszName);
        pICEDevice->ICEDisconnect();
        Drv_UpdateDevStatus(pICEDevice->GetDrvHandle(), DEV_STATUS_BAD);
        SAFE_DELETE(pICEDevice);
    }
    return DRV_SUCCESS;
}
/**
 *  ����麯����tdrv�Ĳ��÷���ģʽ��һ����������һ���飬�������������
 *
 *  @param
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum, TagInfo *pOutDevTags, unsigned int *pnTagsNum,
                                   TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
    if (NULL == pOutDevTags || NULL == pnTagsNum || NULL == pTagGrps || NULL == pnTagGrpsNum)
        return -1;

    // �趨��Ҫ��ܸ��µĵ㣬û��ɸѡ�������������ã���ʹ�ö��ٵ㡣û����д��ַ�ĵ��Ѿ���������
    std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);
    *pnTagsNum = nTagsNum;

    // �趨��Ҫ��ȡ�����ݿ����
    memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));
    map<string, string> checkGrpAddress; // ��������Ƿ��е�ַ������TagGrps
    // TagGroupInfo��Ӧ���ݿ飬������һ����ַһ�����ݿ飬���ŵ�һ���߳�����
    for (int i = 0, indexGrp = 0; i < nTagsNum; ++i)
    {
        string tempGrpName = "group_" + to_string(pOutDevTags[i].nTagID); // group_tagid ��Ϊgroup������
        // ���szAddress�Ƿ��ظ�
        map<string, string>::iterator resultIte = checkGrpAddress.find(pOutDevTags[i].szAddress);
        if (resultIte != checkGrpAddress.end()) // �Ѿ����ڵĲ����ٲ���
        { // ��ͬ�ĵ�ַ����tag���szGrpName�����Ѿ����ڵ�name����ͬszGrpName��tag���ڸ���ֵʱ��ʹ����ͬ��ַ��DataBlock���и���
            Safe_CopyString(pOutDevTags[i].szGrpName, resultIte->second.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
            continue;
        }
        else
        {
            Safe_CopyString(pOutDevTags[i].szGrpName, tempGrpName.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
            checkGrpAddress.insert(make_pair(pOutDevTags[i].szAddress, pOutDevTags[i].szGrpName));
        }
        // Safe_CopyString(pOutDevTags[i].szGrpName, tempGrpName.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
        // pair<set<string>::iterator, bool> result = checkGrpAddress.insert(pOutDevTags[i].szAddress);
        // if (result.second == false) // �Ѿ����ڵĲ����ٲ���
        // {
        // 	continue;
        // }
        Safe_CopyString(pTagGrps[indexGrp].szGroupName, tempGrpName.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
        Safe_CopyString(pTagGrps[indexGrp].szAddress, pOutDevTags[i].szAddress, ICV_IOADDR_MAXLEN + 1);

        switch (pOutDevTags[i].nDataType)
        {
        case TAG_DATATYPE_LREAL:
        case TAG_DATATYPE_LINT:
        case TAG_DATATYPE_ULINT:
        case TAG_DATATYPE_LWORD:
        case TAG_DATATYPE_LTIME:
            pTagGrps[indexGrp].nElemNum = 8;
            break;

        case TAG_DATATYPE_INT:
        case TAG_DATATYPE_UINT:
        case TAG_DATATYPE_WORD:
            pTagGrps[indexGrp].nElemNum = 2;
            break;

        case TAG_DATATYPE_REAL:
        case TAG_DATATYPE_UDINT:
        case TAG_DATATYPE_DINT:
        case TAG_DATATYPE_DWORD:
        case TAG_DATATYPE_TIME:
        case TAG_DATATYPE_DATE:
        case TAG_DATATYPE_TOD:
        case TAG_DATATYPE_DT:
            pTagGrps[indexGrp].nElemNum = 4;
            break;

        case TAG_DATATYPE_BOOL:
        case TAG_DATATYPE_SINT:
        case TAG_DATATYPE_USINT:
        case TAG_DATATYPE_BYTE:
        case TAG_DATATYPE_CHAR:
            pTagGrps[indexGrp].nElemNum = 1;
            break;
        case TAG_DATATYPE_STRING:
            // �ݶ�Ϊ256
            pTagGrps[indexGrp].nElemNum = STRING_BLOCK_SIZE;
            break;
        default:
            break;
        }
        pTagGrps[indexGrp].nElemBits = BITS_PER_BYTE;
        pTagGrps[indexGrp].nPollRate = 0; // ���ˢ���ʾ������ݿ��ˢ����
        // type �ȴ�tag���type ʹ��c++11�ı�׼��to_stringҲû����
        strcpy(pTagGrps[indexGrp].szGroupType, std::to_string(pOutDevTags[i].nDataType).c_str());
        indexGrp++;
        *pnTagGrpsNum = indexGrp; // ��¼pnTagGrpsNum��
        CV_DEBUG(g_CVLogICEDrv, "Tag \"%s\" details: address %s group %s nBitOffset %d", pOutDevTags[i].szTagName,
                 pOutDevTags[i].szAddress, pOutDevTags[i].szGrpName, pOutDevTags[i].nBitOffSet);

        g_tagScanIntv.insert({tempGrpName, pDevTags[i].nPollRate});
    }
    return DRV_SUCCESS;
}
/**
 *
 *  @param
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDatablock);
    ICEDevice *pICEDevice = static_cast<ICEDevice *>(Drv_GetUserDataPtr(hDevice, 0));
    Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE); // ���ó�ʼ״̬��δ�����豸

    int32 nBlockType = atoi(pDataBlock->pszBlockType);
    if (nBlockType != TAG_DATATYPE_STRING && nBlockType != TAG_DATATYPE_LREAL && nBlockType != TAG_DATATYPE_LINT &&
        nBlockType != TAG_DATATYPE_BOOL)
    {
        CV_WARN(g_CVLogICEDrv, -1, "[%s]pDataBlock->pszBlockType[%d] not support!", pDataBlock->pszAddress, nBlockType)
        return DRV_SUCCESS;
    }

    ICEDataBlock tDataBlock;
    GetNameFromAddress(tDataBlock.strName, pDataBlock->pszAddress); // �ӵ�ַ�еõ�name
    tDataBlock.nBlockType = nBlockType;
    tDataBlock.nSize = pDataBlock->nElemNum;
    tDataBlock.hDatablock = hDatablock;
    tDataBlock.hDevice = hDevice;

    g_DataBloackMap.insert(make_pair(tDataBlock.strName, tDataBlock));
    CV_INFO(g_CVLogICEDrv, "Add blockname[%s] , address[%s] to device[%s]", tDataBlock.strName.c_str(),
            pDataBlock->pszAddress, pDevice->pszName);

    return DRV_SUCCESS;
}

/**
 *  ɾ�����ݿ�ʱ�Ĳ���
 *
 *  @param
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version
 */

CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
    ICEDevice *pICEDevice = static_cast<ICEDevice *>(Drv_GetUserDataPtr(hDevice, 0));
    // ��Ϊ����һ��topic��ע�ᣬû����������ע��
    // for (map<string, string>::iterator ite = g_TopicIdDeviceName.begin(); ite != g_TopicIdDeviceName.end(); ite++)
    //{
    //	// ����ע��
    //	pICEDevice->t3UnSubscribe(ite->first);
    //}
    Drv_UpdateBlockStatus(hDevice, hCfgDataBlock, DATA_STATUS_COMM_FAILURE);
    return DRV_SUCCESS;
}

/*
 *  @version     4/14/2023    Initial Version.
 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset,
                                 char *szCmdData, int nCmdDataLenBits)
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDatablock);
    ICEDevice *pICEDevice = static_cast<ICEDevice *>(Drv_GetUserDataPtr(hDevice, 0));
    if (NULL == pICEDevice)
        return EC_ICV_DA_NULL_POINTER;

    if (pICEDevice->m_bStop == true) // TODO(wuzheqiang)
    {
        CV_WARN(g_CVLogICEDrv, -1, "icedevice status is Stop, can not write!")
        return -1;
    }

    if (nCmdDataLenBits <= 0 || nCmdDataLenBits / BITS_PER_BYTE > 512)
        return EC_ICV_DA_WRITEMSG_LENGTH_ERROR;

    int nCmdByteLen =
        nCmdDataLenBits % BITS_PER_BYTE == 0 ? nCmdDataLenBits / BITS_PER_BYTE : nCmdDataLenBits / BITS_PER_BYTE + 1;

    // ��־��¼�����豸�����ݿ飬λƫ�ƣ�����
    char szBuffer[ICV_BLOBVALUE_MAXLEN];
    unsigned int nHexBufferLen = ICV_BLOBVALUE_MAXLEN;
    memset(szBuffer, 0, sizeof(szBuffer));
    cvcommon::HexDumpBuf((unsigned char *)szCmdData, nCmdByteLen, szBuffer, &nHexBufferLen);
    CV_INFO(g_CVLogICEDrv,
            "CTRL:Devive[%s] Block[%s] StartAddr[%s] ByteOffset[%d] BitOffset[%d] Write[%s] WriteLen[%d].",
            pDevice->pszName, pDataBlock->pszName, pDataBlock->pszAddress, nTagByteOffset, nTagBitOffset, szBuffer,
            nCmdByteLen);

    std::string strName = "";
    GetNameFromAddress(strName, pDataBlock->pszAddress); // �ӵ�ַ�еõ�name
    auto iter = g_DataBloackMap.find(strName);
    if (iter == g_DataBloackMap.end())
    {
        CV_WARN(g_CVLogICEDrv, -1, "Tagname %s not found data block ", strName.c_str());
        return -1;
    }
    // DSF::DataUnitPtr d = new DSF::DataUnit; // Ҳ��һ������ָ�룬������Զ��ͷ�
    DSF::DataUnit d;
    d.strName = iter->second.strName;
    d.lTime = NowMillisecond();
    switch (iter->second.nBlockType)
    {
    case TAG_DATATYPE_BOOL:
        d.eType = DSF::ValueType::Boolean;
        d.bValue = *(bool *)szCmdData;
        break;
    case TAG_DATATYPE_LINT:
        d.eType = DSF::ValueType::Integer;
        d.lValue = *(int32 *)szCmdData;
        break;
    case TAG_DATATYPE_LREAL:
        d.eType = DSF::ValueType::Decimal;
        d.dValue = *(double *)szCmdData;
        break;
    case TAG_DATATYPE_STRING:
        d.eType = DSF::ValueType::Text;
        d.strValue = szCmdData;
        break;
    default:
        CV_WARN(g_CVLogICEDrv, -1, "Tagname %s not support data type ", strName.c_str());
        return -1;
    }
    pICEDevice->ICECacheData(d);

    return DRV_SUCCESS;
}

///*
// *  ��ʱ���ͻ��������
// *  @version     4/14/2023    Initial Version.
// */

CVDRIVER_EXPORTS long OnDeviceTimer(DRVHANDLE hDevice)
{
    // ÿ���豸�����2����ѯ���������״̬
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    ICEDevice *pICEDevice = static_cast<ICEDevice *>(Drv_GetUserDataPtr(hDevice, 0));
    CV_DEBUG(g_CVLogICEDrv, "OnDeviceTimer start Device name[%s]", pDevice->pszName);

    pICEDevice->ICESendData();
    return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
    // Ϊ�˱���������ܴ�ӡ����������־��������Ӹÿշ���
    return DRV_SUCCESS;
}
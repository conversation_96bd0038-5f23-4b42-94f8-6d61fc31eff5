/**************************************************************
 *  Filename:    SockHandler.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: SockHandler.cpp
 *
 *  @author:     lijingjing
 *  @version     05/29/2008  lijingjing  Initial Version
 *  @version     06/01/2011  chenzhiquan  ���ص����������������ֳ�����
 *                                        ��������
**************************************************************/

#include <ace/ACE.h>
#include "TcpClientHandler.h"
#include "common/CVLog.h"
#include "common/cvGlobalHelper.h"
#include <ace/OS_NS_sys_socket.h>
#include <ace/SOCK_SEQPACK_Association.h>
#include <string>
#include "driversdk/cvdrivercommon.h"
#include "common/LogHelper.inl"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include "gettext/libintl.h"
#include "common/CommHelper.h"

#ifdef __sun
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#endif


 //FIXME:ɾ����ͷ�ļ���ĺ궨�壬���濴�������ĺ���

#define DRV_DEV_SATATUS_QUEUE_MAX 1000

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING

extern string	g_strDrvName;
extern CCVLog g_CVDrvierCommonLog;
extern CSimpleThreadQueue<ACE_Message_Block*> g_devStatusQueue;
extern bool IsActiveHost();
extern void proto_devstatus_pack(const char* szDevName, const TCV_TimeStamp* pCVTime, long nDrvStatus, char** ppBuf, int32* pnLen);
/**
 *  ���캯��.
 *
 *  @param  -[in]  ACE_Reactor*  r: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
CTcpClientHandler::CTcpClientHandler() 
{
	// Ϊÿ���豸��һ���߳�
	m_szRecvBuf = new char[DEFAULT_INPUT_SIZE];

	m_strCurIPAddress = "";
	m_nPort = 0;
	m_nPort2 = 0;
	m_nCurPort = 0;
	m_pfnRecvCallback = NULL;
	m_pfnConnectCallback = NULL;
	m_pCallbackParam = NULL;
	m_lCallbackParam = 0;
	memset(m_szCallbackParam, 0, sizeof(m_szCallbackParam));

	m_bConnected = false;
	m_bEnableConnect = true;
	m_bPrimaryDevActive = true;
	m_nConnTimeOut = 3;	// 3��ȱʡ
	m_tmLastConnect = 0;
	m_bMultiLink = false;				// �Ƿ�֧�ֶ�����
	m_usLocalPort = 0;
}

/**
 *  ��������.
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
CTcpClientHandler::~CTcpClientHandler()
{
	DisConnect();

	if(m_szRecvBuf)
		delete [] m_szRecvBuf;
	m_szRecvBuf = NULL;
}

// ���Ӳ����������tcp�����:IP,Port,��ѡ�����ӳ�ʱ���Ժ��ǿ����д���
// ����һ����tcpclient���ӷ�ʽ����ʽ: IP:***********;Port:502;ConnTimeOut:3000
long CTcpClientHandler::SetConnectParam(char *szConnParam)
{
	string strConnParam = szConnParam;
	CV_INFO(g_CVDrvierCommonLog, _("Set connection parameter %s"), szConnParam);
	strConnParam += ";"; // ����һ���ֺ�
	int nPos = strConnParam.find(';');
	while(nPos != string::npos)
	{
		string strOneParam = strConnParam.substr(0, nPos);
		strConnParam = strConnParam.substr(nPos + 1);// ��ȥ��һ���Ѿ��������Ĳ���
		nPos = strConnParam.find(';');

		if(strOneParam.empty())
			continue;

		int nPosPart = strOneParam.find('=');	// IP:**********
		if(nPosPart == string::npos)
			continue;

		// ��ȡ��ĳ���������ƺ�ֵ
		string strParamName = strOneParam.substr(0, nPosPart); // e.g. IP
		string strParamValue = strOneParam.substr(nPosPart + 1); // e.g. **********

		if(ACE_OS::strcasecmp("ip", strParamName.c_str()) == 0)
			m_strMainIPAddress = strParamValue;
		if(ACE_OS::strcasecmp("ip2", strParamName.c_str()) == 0)
			m_strBakeIPAddress = strParamValue;
		else if(ACE_OS::strcasecmp("port", strParamName.c_str()) == 0)
			m_nPort = ::atoi(strParamValue.c_str());
		else if(ACE_OS::strcasecmp("port2", strParamName.c_str()) == 0)
			m_nPort2 = ::atoi(strParamValue.c_str());
		else if(ACE_OS::strcasecmp("conntimeout", strParamName.c_str()) == 0)
			m_nConnTimeOut = ::atoi(strParamValue.c_str());
		else if(ACE_OS::strcasecmp("multilink", strParamName.c_str()) == 0)
			m_bMultiLink = ::atoi(strParamValue.c_str());
		else if (ACE_OS::strcasecmp("localport", strParamName.c_str()) == 0)
			m_usLocalPort = ::atoi(strParamValue.c_str());
		else
			;
	}

	m_strCurIPAddress = m_strMainIPAddress;
	m_nCurPort = m_nPort;
	m_bPrimaryDevActive = true;

	m_tmLastConnect = 0;	// ʹ�ÿ���������������

	return DRV_SUCCESS;
}

/**
 *  �����豸.
 *
 *
 *  @version     07/01/2008  lijingjing  Initial Version.
 *  @version	8/13/2013  baoyuansong  ���Ӷ����ӷ�����ʱָ�����ض˿ڵ�֧��.
 *  @version	9/2/2013  baoyuansong  �޸ı��ض˿�����.
 */
 long CTcpClientHandler::Connect()
{
	// �����Ѿ�����
	if (m_bConnected)
		return DRV_SUCCESS;

	// ���������������ڵ������豸���Ǳ��������
	if(!m_bMultiLink && !IsActiveHost())
	{
		// ACE_Time_Value timeValue = ACE_OS::gettimeofday();
		// TCV_TimeStamp timeStamp;
		// timeStamp.tv_sec = (uint32_t)timeValue.sec();
		// timeStamp.tv_usec = (uint32_t)timeValue.usec();
		// char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
		// cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);

		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
		return -100;
	}

	// С��2����С���ӳ�ʱ��������
	time_t tmNow;
	time(&tmNow);
	int nTimeSpan = (int)abs(tmNow - m_tmLastConnect);
	if(nTimeSpan < m_nConnTimeOut * 2)
		return - 1;

	if (0 != m_tmLastConnect)
		SwitchServAddr();

	time(&m_tmLastConnect);

	ACE_INET_Addr destAddr(m_nCurPort, m_strCurIPAddress.c_str());
	// ȷ�������ܽ���������
	close();
	set_handle(ACE_INVALID_HANDLE);	

	CONNECTOR connector(reactor());

	// ����Ĭ�����ӳ�ʱ
	ACE_Time_Value timeout(m_nConnTimeOut);
	ACE_Synch_Options synch_option(ACE_Synch_Options::USE_TIMEOUT, timeout);
	
	// ����Ϊͬ����ʽ���Ա�Ϊ�����������׼��
	peer().disable(ACE_NONBLOCK);

	// ���������������
	// ����״̬�ĸı�OnConnectStateChange�����¼��ص��д�����
	CTcpClientHandler *pSockHandler = this;

	int nRet = 0;
	if (m_usLocalPort >0)
	{
		ACE_INET_Addr localAddr(m_usLocalPort);
		nRet = connector.connect(pSockHandler, destAddr, synch_option, localAddr, 1);
	}
	else
		nRet = connector.connect(pSockHandler, destAddr, synch_option);

	std::string strSubKeyPrefix = g_strDrvName;
	strSubKeyPrefix += "#";
	strSubKeyPrefix += m_strDeviceName;
	// drv_redis_batchbegin();
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_NAME, m_strDeviceName.c_str());
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_ADDR, m_strCurIPAddress.c_str());
	// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_PORT, m_nCurPort);
	//drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONSTATUS, nRet);
	// drv_redis_batchaddtime(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_STATUS);
	// drv_redis_batchsubmit();
	if(nRet == -1)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Connect to device %s (%s: %d) failed!"), m_strDeviceName.c_str(), m_strCurIPAddress.c_str(), m_nCurPort);//�����豸(%s: %d)ʧ��!
		return -1;
	}

	//��ȡ����IP�Ͷ˿���Ϣ
	ACE_INET_Addr localInfo;
	size_t addr_size = 1;
	ACE_SOCK_SEQPACK_Association ssa(pSockHandler->get_handle());
	ssa.get_local_addrs(&localInfo, addr_size); // ��ȡ���ӱ��ص�IP�Ͷ˿�
	char szLocalAddr[64];
	memset(szLocalAddr, 0 , sizeof(szLocalAddr));
	localInfo.addr_to_string(szLocalAddr, 63); // ��ʽΪIP:Port

	CV_INFO(g_CVDrvierCommonLog, _("Connect to device%s (%s: %d) successfully! local addr: %s (config port: %d)"), m_strDeviceName.c_str(), m_strCurIPAddress.c_str(), m_nCurPort, szLocalAddr, m_usLocalPort);//�����豸(%s: %d)�ɹ�!
	
#ifdef __sun
	//����solarisʵ�������������жϺ��5�����ڷ�����sendһֱ������ȷ�ķ����ֽ�������-1����Ҫͨ��TCPЭ���keepalive���������̼�⵽�����жϵ�ʱ�� by wangyadong 20141229
	int nKeepAlive = 1;
	int nTCPKeepaliveThreshold = 10000;
	int nTCPKeepaliveAbortThreshold = 1000;
	int nTCPRTOMax = 1000;
	int nTCPAbortThreshold = 3000;
	nRet = this->peer().set_option(SOL_SOCKET, SO_KEEPALIVE, &nKeepAlive, sizeof(nKeepAlive));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_KEEPALIVE_THRESHOLD, &nTCPKeepaliveThreshold, sizeof(nTCPKeepaliveThreshold));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_KEEPALIVE_ABORT_THRESHOLD , &nTCPKeepaliveAbortThreshold, sizeof(nTCPKeepaliveAbortThreshold));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_RTO_MAX, &nTCPRTOMax, sizeof(nTCPRTOMax));
	nRet = this->peer().set_option(IPPROTO_TCP, TCP_ABORT_THRESHOLD, &nTCPAbortThreshold, sizeof(nTCPAbortThreshold));
#endif
	return DRV_SUCCESS;
}

 /**
 *  �Ͽ�����.
 *
 *
 *  @version     07/01/2008  lijingjing  Initial Version.
 */
 long CTcpClientHandler::DisConnect()
{
	if (m_bConnected)	// ��������״̬
	{
		// �ر����ӡ��ƺ����õ���close���������
		ACE_OS::shutdown(get_handle(), ACE_SHUTDOWN_BOTH);
		peer().close();
		set_handle(ACE_INVALID_HANDLE);
		//int nRet = ACE_OS::closesocket(m_sockHandler.get_handle()); 
		OnConnectStateChange(false);
		CV_INFO(g_CVDrvierCommonLog, "Disconnect with %s(%s:%d)", this->m_strDeviceName.c_str(),  m_strCurIPAddress.c_str(), m_nCurPort);
	}

	return DRV_SUCCESS;
}

 void CTcpClientHandler::CheckConnect()
 {
	 if(!m_bConnected)
		 Connect();
 }

 void CTcpClientHandler::OnConnectStateChange(bool bConnected)
 {
	 m_bConnected = bConnected;

	 //�����豸״̬
	 if (g_devStatusQueue.size()< DRV_DEV_SATATUS_QUEUE_MAX)
	 {
		 TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
		 char* pBuf = NULL;
		 int32 nLenBuf = 0;
		 proto_devstatus_pack(m_strDeviceName.c_str(), &cvTimeStamp, m_bConnected, &pBuf, &nLenBuf);
		 ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
		 pMsg->copy(pBuf, nLenBuf);
		 SAFE_DELETE_ARRAY(pBuf);
		 g_devStatusQueue.enqueue(pMsg);
	 }

	 if(m_pfnConnectCallback)
		 m_pfnConnectCallback(bConnected, m_lCallbackParam, m_pCallbackParam, m_szCallbackParam);
 }
/**
 *  ��������ʱ������.
 *
 *  @param  -[in]  void*  p: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
int CTcpClientHandler::open(void *p)
{
	// ���ú���Ľ��պͷ���Ϊ�첽��ʽ
	if(this->peer().enable(ACE_NONBLOCK) == -1)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1,  _("Establish connection with %s, set asynchronization mode failed, exit"), 
			this->m_strDeviceName.c_str());
		return -1;
	}

	if (super::open (p) == -1)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Initiate connection successfully��but open(p) failed"));
		return -1;
	}
	
	ACE_INET_Addr deviceAddr;
	if (this->peer().get_remote_addr (deviceAddr) != 0)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Received connection request from device, but failed to get remote address, and then disconnected with device"));
		return -1;
	}

	// ��ȡ�Զ˵�IP��Port��Ϣ
	string strDeviceIP = deviceAddr.get_host_addr();
	unsigned short uDevicePort = deviceAddr.get_port_number();
	CV_INFO(g_CVDrvierCommonLog, _("Connected to %s(%s:%d)"), this->m_strDeviceName.c_str(),  strDeviceIP.c_str(), uDevicePort);

	m_bConnected = true;
	 if (g_devStatusQueue.size()< DRV_DEV_SATATUS_QUEUE_MAX)
	 {
		 //�����豸״̬
		 TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
		 char* pBuf = NULL;
		 int32 nLenBuf = 0;
		 proto_devstatus_pack(m_strDeviceName.c_str(), &cvTimeStamp, DEV_STATUS_GOOD, &pBuf, &nLenBuf);
		 ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
		 pMsg->copy(pBuf, nLenBuf);
		 SAFE_DELETE_ARRAY(pBuf);
		 g_devStatusQueue.enqueue(pMsg);
	 }

	if(m_pfnConnectCallback)
		m_pfnConnectCallback(true, m_lCallbackParam, m_pCallbackParam, m_szCallbackParam);
	return 0;
}

/**
 *  ��������ʱ�ú���������.
 *
 *  @param  -[in]    ACE_HANDLE: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 *  @version     09/25/2010  chenzhiquan ���յ��ļ��������־
 */
int CTcpClientHandler::handle_input(ACE_HANDLE)
{	
	if(m_pfnRecvCallback == NULL)//huangsongxin �ص�����Ŀǰû�á���������������ݻ�Ӱ�쵽�������շ�
	{
		return DRV_SUCCESS;
	}
	// ��������
	this->peer().enable(ACE_NONBLOCK);  
	ssize_t nRecvBytes = this->peer().recv(m_szRecvBuf, DEFAULT_INPUT_SIZE);

	if(nRecvBytes == 0) //connection loss  
		return -1;
	else if (nRecvBytes == -1) // block  
	{  
		if (errno == EWOULDBLOCK)  
			return 0;  
		else   
			return -1;  
	}  
	while(nRecvBytes > 0)
	{
		if(m_pfnRecvCallback)
			m_pfnRecvCallback(m_szRecvBuf, nRecvBytes, m_lCallbackParam, m_pCallbackParam, m_szCallbackParam);

		// ���ܻ�������δ��ȫ������������������
		if(nRecvBytes != DEFAULT_INPUT_SIZE)
			break;

		// ���ܻ�������δ��ȫ������������������
		// if(nRecvBytes == DEFAULT_INPUT_SIZE)
		nRecvBytes = this->peer().recv(m_szRecvBuf, DEFAULT_INPUT_SIZE);
		if(nRecvBytes == 0) //connection loss  
			return -1;
		else if (nRecvBytes == -1) // block  
		{  
			if (errno == EWOULDBLOCK)  
				return 0;  
			else   
				return -1;  
		}
	}

	return DRV_SUCCESS;
}

/**
 *  �������ʱ�ú���������.
 *
 *  @param  -[in]  ACE_HANDLE  fd: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
int CTcpClientHandler::handle_output (ACE_HANDLE fd /* = ACE_INVALID_HANDLE */)
{
	
	return 0;
}

/**
 *  ��SockHandler��ACE_Reactor���Ƴ�ʱ�ú���������.
 *
 *  @param  -[in]  ACE_HANDLE  handle: [comment]
 *  @param  -[in]  ACE_Reactor_Mask  close_mask: [comment]
 *
 *  @version     05/29/2008  lijingjing  Initial Version.
 */
int CTcpClientHandler::handle_close(ACE_HANDLE handle, ACE_Reactor_Mask close_mask)
{	
	ACE_TRACE((LM_TRACE, ACE_TEXT("SockHandler::handle_close()\n")));
	this->reactor()->remove_handler
		(this->get_handle (),
		// Remove all the events for which we��re
		// registered. We must pass the DONT_CALL
		// flag here to avoid infinite recursion.
		ACE_Event_Handler::ALL_EVENTS_MASK |
		ACE_Event_Handler::DONT_CALL);

	// ����Ϊͬ����ʽ���Ա�Ϊ�����������׼��
	this->peer().disable(ACE_NONBLOCK);

	OnConnectStateChange(false);
	ACE_OS::shutdown(this->get_handle(), 0x02);
	ACE_OS::closesocket(this->get_handle());
	set_handle(ACE_INVALID_HANDLE);
	CV_INFO(g_CVDrvierCommonLog, "Disconnected with %s(%s:%d)", this->m_strDeviceName.c_str(),  m_strCurIPAddress.c_str(), m_nCurPort);

	return 0;
}

/**
 *  ��������.
 *
 *  @param  -[in]  char*  pszSendBuf: [comment]
 *  @param  -[in]  int  nSendBytes: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
long CTcpClientHandler::Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs)
{
	ACE_Time_Value timeValue = ACE_OS::gettimeofday();
	TCV_TimeStamp timeStamp;
	timeStamp.tv_sec = (uint32_t)timeValue.sec();
	timeStamp.tv_usec = (uint32_t)timeValue.usec();
	char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
	cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);

	// ���û��������������
	if(!m_bConnected)
	{
		// ���������������ڵ������豸���Ǳ��������
		if(!m_bMultiLink && !IsActiveHost())
		{
			// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
			// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
			// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
			// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
			// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
			// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
			// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
			return -100;
		}
	}

	// �������豸���������������
	if(!m_bConnected)
		Connect();

	// ���������Ӳ���
	if(!m_bConnected)
	{
		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
		return -101;
	}

	// ��������
	ssize_t nSent = 0;
	//if(nAsyncMode)
	//	nSent = this->peer().send(pszSendBuf, nSendBytes, 0);
	//else
	{
		ACE_Time_Value tvTimeout(0, 1000 * lTimeoutMs);

		nSent = this->peer().send_n(pszSendBuf, nSendBytes, &tvTimeout); // �������������˲�������ķ���������
		CV_DEBUG(g_CVDrvierCommonLog, _("-----------send_n return value %d, errno %d"), nSent, errno);
	}

	if (nSent == -1)
	{
		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);

		if(errno != EWOULDBLOCK)
		{
			// ������δ֪�쳣�����������ʱ������ʱ�������Ƿ���-1����Ҫ�Ͽ�����
			if(this->get_handle() != 0) 
			{
				ACE_OS::shutdown(this->get_handle(), 0x02);
				ACE_OS::closesocket(this->get_handle());
				set_handle(ACE_INVALID_HANDLE);
				m_bConnected = false;

				//�����豸״̬
				 if (g_devStatusQueue.size()< DRV_DEV_SATATUS_QUEUE_MAX)
				{
					TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
					char* pBuf = NULL;
					int32 nLenBuf = 0;
					proto_devstatus_pack(m_strDeviceName.c_str(), &cvTimeStamp, DEV_STATUS_BAD, &pBuf, &nLenBuf);
					ACE_Message_Block*  pMsg = new ACE_Message_Block(nLenBuf);
					pMsg->copy(pBuf, nLenBuf);
					SAFE_DELETE_ARRAY(pBuf);
					g_devStatusQueue.enqueue(pMsg);
				}

				CV_ERROR(g_CVDrvierCommonLog, -1, "%s(%s:%d)connection closed, errno =  %d.", m_strDeviceName.c_str(), m_strCurIPAddress.c_str(), m_nCurPort, errno);
			}
			return nSent;
		}
		else
			return nSent;
	}

	if (nSent != nSendBytes)		// ����ʧ��,�����Ƿ��������ã����Բ��ܹر�socket
	{
		if(this->get_handle() != 0) 
		{
			// ACE_OS::shutdown(this->get_handle(), 0x02);
			// ACE_OS::closesocket(this->get_handle());
		}

		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", CV_STATUS_DEVICE_CONNFAIL, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
		return nSent; // EC_ICV_DA_IO_SEND_ERROR;
	}
	
// 	char szKey[ICV_HOSTNAMESTRING_MAXLEN];
// 	memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
// 	ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strDeviceName.c_str());
// 	char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
// 	memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
// 	ACE_OS::snprintf(szStatus, sizeof(szStatus), "0;%s", szTime);
// 	CV_DEBUG(g_CVDrvierCommonLog, _("-----------send_n STATERW_SetStringCommand szKey :%s"), szKey);
// 
// 	STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
// 	CV_DEBUG(g_CVDrvierCommonLog, _("-----------send_n STATERW_SetStringCommand szStatus :%s"), szStatus);

	//modify by hsx

	return nSent;
}


/**
 *  ��������.
 *
 *  @param  -[in]  char*  pszSendBuf: [comment]
 *  @param  -[in]  int  nSendBytes: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
long CTcpClientHandler::Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs)
{
	CheckConnect();

	// ���û��������������
	if(!m_bConnected)
	{
		// ���������������ڵ������豸���Ǳ��������
		if(!m_bEnableConnect)
			return -1;

		Connect();
		// ������������Ȼû��������
		if(!m_bConnected)
			return -1;
	}

	ACE_Time_Value tvTimeout(0, 1000 * lTimeoutMs);
	// ��������
	ssize_t nSent = 0;
	nSent = this->peer().recv((void *)szRecvBuf, nSendBytes, &tvTimeout);
	return nSent;
}

/**
 *  ��������.
 *
 *  @param  -[in]  char*  pszSendBuf: [comment]
 *  @param  -[in]  int  nSendBytes: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
long CTcpClientHandler::Recv_n(char* szRecvBuf, long nSendBytes, long lTimeoutMs)
{
	CheckConnect();

	// ���û��������������
	if(!m_bConnected)
	{
		// ���������������ڵ������豸���Ǳ��������
		if(!m_bEnableConnect)
			return -1;

		Connect();
		// ������������Ȼû��������
		if(!m_bConnected)
			return -1;
	}

	ACE_Time_Value tvTimeout(0, 1000 * lTimeoutMs);
	// ��������
	ssize_t nSent = 0;
	nSent = this->peer().recv_n((void *)szRecvBuf, nSendBytes, &tvTimeout);
	return nSent;
}

void CTcpClientHandler::ClearDeviceRecvBuffer()
{
	char szBuffer[2048];

    int nRecvBytes = 0;
    while((nRecvBytes = Recv(szBuffer, sizeof(szBuffer), 0)) > 0)
	{
        if (nRecvBytes < sizeof(szBuffer))
            break;
	}
}

long CTcpClientHandler::Start()
{
	return Connect();
}

long CTcpClientHandler::Stop()
{
	return DisConnect();
}

void CTcpClientHandler::SwitchServAddr()
{
	if(!m_strBakeIPAddress.empty() && m_strBakeIPAddress.compare(m_strMainIPAddress) != 0)//����IP��Ϊ�ղ���������IP��ͬ
	{
		if(m_bPrimaryDevActive)
		{
			m_strCurIPAddress = m_strBakeIPAddress;
			if (m_nPort2 != 0)
				m_nCurPort = m_nPort2;
			else
				m_nCurPort = m_nPort;
		}
		else
		{
			m_strCurIPAddress = m_strMainIPAddress;
			m_nCurPort = m_nPort;
		}

		std::string strSubKeyPrefix = g_strDrvName;
		strSubKeyPrefix += "#";
		strSubKeyPrefix += m_strDeviceName;
		// drv_redis_time_t(strSubKeyPrefix.c_str(), DRV_REDIS_TIME_DEVSWITCH);
		m_bPrimaryDevActive = !m_bPrimaryDevActive;
	}
}


long CTcpClientHandler::GetConnectedState()
{
	if(m_strCurIPAddress.compare(m_strMainIPAddress) == 0 )
		return MAINISCONNECTED;
	else if(m_strCurIPAddress.compare(m_strBakeIPAddress) == 0)
		return BACKISCONNECTED;
	else
		return 0;
}

void CTcpClientHandler::SetRecvCallBackFunc(PFN_CVCommRecvCallBack pfnRecvFunc, void* pCallbackParam, long lCallbackParam, char *pszCallbackParam)
{
	m_pfnRecvCallback = pfnRecvFunc;
	m_pCallbackParam = pCallbackParam;
	m_lCallbackParam = lCallbackParam;
	memcpy(m_szCallbackParam, pszCallbackParam, ICV_DEVICENAME_MAXLEN);
}